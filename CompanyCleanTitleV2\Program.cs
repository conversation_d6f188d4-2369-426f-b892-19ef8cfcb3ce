using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using LanguageDetection;

namespace CompanyCleanTitleV2
{
    internal class Program
    {
        // Static language detector initialized once for the entire application lifecycle
        private static readonly Lazy<LanguageDetector> _languageDetector = new Lazy<LanguageDetector>(() =>
        {
            var detector = new LanguageDetector();
            detector.AddAllLanguages();
            return detector;
        });

        // Cache for language detection results to avoid repeated detection of the same strings
        private static readonly ConcurrentDictionary<string, string> _languageCache = new ConcurrentDictionary<string, string>();

        // Maximum cache size to prevent memory issues
        private const int MaxCacheSize = 10000;

        static void Main(string[] args)
        {
            // Run performance benchmark first
            RunPerformanceBenchmark();

            Console.WriteLine("\n" + new string('=', 50) + "\n");

            // Test cases for GetDeepCleanCompanyNameV1
            var testCases = new Dictionary<string, string>
            {
                { "Kellogg Brown & Root LLC", "Kellogg Brown Root" },
                { "Siemens AG", "Siemens" },
                { "Telefónica S.A.", "Telefónica" },
                { "株式会社東芝", "東芝" },
                { "भारत लिमिटेड", "भारत" },
                { "Bayerische Motoren Werke AG", "Bayerische Motoren Werke" },
                { "Nestlé S.A.", "Nestlé" },
                { "Gazprom OAO", "Gazprom" },
                { "Companhia Vale do Rio Doce S.A.", "Companhia Vale do Rio Doce" },
                { "Volkswagen Aktiengesellschaft", "Volkswagen" },
                { "Samsung Electronics Co., Ltd.", "Samsung Electronic" },
                { "Tata Consultancy Services Limited", "Tata Consultancy Service" },
                { "中国石油天然气集团公司", "中国石油天然气集团公司" }, // Should remain unchanged if no suffix
                { "شركة أرامكو السعودية", "شركة أرامكو السعودية" }, // Arabic, no suffix
                { "한국전력공사", "한국전력공사" }, // Korean, no suffix
            };

            Console.WriteLine("Testing GetDeepCleanCompanyNameV1:");
            Console.WriteLine("=================================");

            foreach (var test in testCases)
            {
                var result = GetDeepCleanCompanyName(test.Key);
                var passed = result == test.Value;
                Console.WriteLine($"Input: {test.Key}");
                Console.WriteLine($"Expected: {test.Value}");
                Console.WriteLine($"Got: '{result}'");
                Console.WriteLine($"Test {(passed ? "PASSED" : "FAILED")}");
                Console.WriteLine("-------------------");
            }

            Console.WriteLine("\n" + new string('=', 50) + "\n");

            // Test cases for GetCleanJobPostingTitle
            var jobTitleTestCases = new Dictionary<string, string>
            {
                { "Counselor I - (LAC)", "Counselor I" },
                { "Counselor II - (LAC)", "Counselor II" },
                { "Counselor III - (LAC)", "Counselor III" },
                { "Clinician I - (YOP-VSP)", "Clinician I" },
                { "Recreational Therapist - (PGYCC)", "Recreational Therapist" },
                { "Clinician (LCSW) - Los Angeles County Training Center", "Clinician (LCSW)" },
                { "Intensive Case Manager- DHS/JIR", "Intensive Case Manager" },
                { "Catering - Food Service Cook", "Food Service Cook" },
                { "Respiratory Care Therapist II, Transport Team", "Respiratory Care Therapist II" },
                { "Registered Nurse - Operating Room 0630 1.0 FTE", "Registered Nurse, Operating Room" },
                { "Registered Nurse - Critical Care Float (Day/Night)", "Registered Nurse, Critical Care Float" },
                { "Registered Nurse - Cardiac Specialty Unit (Per Diem, Days)", "Registered Nurse, Cardiac Specialty Unit" },
                { "Floors", "Flooring Specialist" },
                { "Sales Associate-5195", "Sales Associate" },
                { "Certified Nurse Assistant Trainee", "Certified Nurse Assistant Trainee" },
                { "Security Officer, Evening Shift, Security Services", "Security Officer" },
                { "Vascular Access Registered Nurse, PT Day Shift, Heart and Vascular Unit", "Vascular Access Registered Nurse" },
                { "Certified Nursing Assistant (CNA), Per Diem Night Shift, Float Pool", "Certified Nursing Assistant (CNA)" },
                { "Registered Nurse (RN), Variable Part Time Day Shift, Operating Room (OR)", "Registered Nurse, Operating Room (OR)" },
                { "Radiology Aide, Evening Shift, Radiology", "Radiology Aide" },
                { "Patient Safety and Risk Management Advisor, Day Shift, Quality & Patient Safety", "Patient Safety and Risk Management Advisor" },
                { "Registered Nurse (RN), Day Shift, Med Surg", "Registered Nurse (RN)" },
                { "Patient Care Technician (CNA), Day Shift, Rehabilitation", "Patient Care Technician (CNA)" },
                { "Pharmacy Technician (Tech II), Rotating Shifts, Pharmacy", "Pharmacy Technician" },
                { "Behavioral Health Therapist, Day Shift, Behavioral Health (Inpatient)", "Behavioral Health Therapist" },
                { "VP, Head of Architecture", "Vice President, Head of Architecture" },
                { "Trust Officer", "Trust Officer" },
                { "Case Designer, Highland Capital Brokerage", "Case Designer" },
                { "Delivery Driver(06032) - 45 Beach Gate Shopping Center", "Delivery Driver" }
            };

            Console.WriteLine("Testing GetCleanJobPostingTitle:");
            Console.WriteLine("================================");

            foreach (var test in jobTitleTestCases)
            {
                var result = GetCleanJobPostingTitle(test.Key);
                var passed = result == test.Value;
                Console.WriteLine($"Input: {test.Key}");
                Console.WriteLine($"Expected: {test.Value}");
                Console.WriteLine($"Got: '{result}'");
                Console.WriteLine($"Test {(passed ? "PASSED" : "FAILED")}");
                Console.WriteLine("-------------------");
            }
        }

        /// <summary>
        /// Benchmarks the performance improvement of the optimized language detection
        /// </summary>
        private static void RunPerformanceBenchmark()
        {
            Console.WriteLine("Performance Benchmark: Language Detection Optimization");
            Console.WriteLine("====================================================");

            var testStrings = new[]
            {
                "Kellogg Brown & Root LLC",
                "Siemens AG",
                "Telefónica S.A.",
                "株式会社東芝",
                "भारत लिमिटेड",
                "Bayerische Motoren Werke AG",
                "Nestlé S.A.",
                "Samsung Electronics Co., Ltd.",
                "Tata Consultancy Services Limited"
            };

            const int iterations = 100;

            // Benchmark optimized version (current implementation)
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            for (int i = 0; i < iterations; i++)
            {
                foreach (var testString in testStrings)
                {
                    DetectLanguageOptimized($"{testString} {i}");
                }
            }

            stopwatch.Stop();
            var optimizedTime = stopwatch.ElapsedMilliseconds;

            Console.WriteLine($"Optimized Implementation:");
            Console.WriteLine($"  - {iterations} iterations × {testStrings.Length} strings = {iterations * testStrings.Length} total detections");
            Console.WriteLine($"  - Total time: {optimizedTime} ms");
            Console.WriteLine($"  - Average per detection: {(double)optimizedTime / (iterations * testStrings.Length):F2} ms");
            Console.WriteLine($"  - Cache hits after first iteration: {_languageCache.Count} cached entries");

            // Test cache effectiveness by running again
            stopwatch.Restart();
            for (int i = 0; i < iterations; i++)
            {
                foreach (var testString in testStrings)
                {
                    DetectLanguageOptimized(testString);
                }
            }
            stopwatch.Stop();
            var cachedTime = stopwatch.ElapsedMilliseconds;

            Console.WriteLine($"\nWith Cache (second run):");
            Console.WriteLine($"  - Total time: {cachedTime} ms");
            Console.WriteLine($"  - Average per detection: {(double)cachedTime / (iterations * testStrings.Length):F2} ms");
            Console.WriteLine($"  - Performance improvement: {((double)(optimizedTime - cachedTime) / optimizedTime * 100):F1}% faster");
        }

        // Dictionary to store language-specific patterns
        private static readonly Dictionary<string, LanguagePatterns> LanguageWordSets = InitializeLanguageWordSets();

        // Dictionary to store job title specific language patterns
        private static readonly Dictionary<string, JobTitleLanguagePatterns> JobTitleLanguageWordSets = InitializeJobTitleLanguageWordSets();

        // Class to hold patterns for each language
        private class LanguagePatterns
        {
            public string SuffixPattern { get; set; }
            public string ConjunctionPattern { get; set; }
            public string PossessivePattern { get; set; }
        }

        // Class to hold job title specific patterns for each language
        private class JobTitleLanguagePatterns
        {
            public string ShiftPattern { get; set; }
            public string DepartmentPattern { get; set; }
            public string EmploymentTypePattern { get; set; }
            public string LocationCodePattern { get; set; }
            public string IdentifierPattern { get; set; }
            public string LocationDetailPattern { get; set; }
        }

        // Initialize language-specific word sets
        private static Dictionary<string, LanguagePatterns> InitializeLanguageWordSets()
        {
            var wordSets = new Dictionary<string, LanguagePatterns>
        {
            // English
            {
                "en", new LanguagePatterns
                {
                    SuffixPattern = @"\b(incorporated|corporation|Corporate|Cooperatives|Cooperative|coop|inc|llc|corp|corps|company|limited|ltd|co|LLP|LP|L\.L\.C|Co\.,? Ltd\.|Co\.|Ltd\.|S\.A\.|S\.A\.?|OAO|Aktiengesellschaft)\b",
                    ConjunctionPattern = @"\b(and|of|in|by|for|or)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // German
            {
                "de", new LanguagePatterns
                {
                    SuffixPattern = @"\b(GmbH|AG|KG|OHG|EG|SE)\b",
                    ConjunctionPattern = @"\b(und|von|in|bei|für|oder)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // French
            {
                "fr", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SA|S\.A\.|SARL|SAS|SE)\b",
                    ConjunctionPattern = @"\b(et|de|à|pour|ou)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Spanish
            {
                "es", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SA|S\.A\.?|SL|SRL)\b",
                    ConjunctionPattern = @"\b(y|de|en|por|para|o)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Italian
            {
                "it", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SpA|SRL|SRLS)\b",
                    ConjunctionPattern = @"\b(e|di|in|per|o)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Portuguese
            {
                "pt", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SA|S\.A\.|Lda)\b",
                    ConjunctionPattern = @"\b(e|de|em|por|para|ou)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Dutch
            {
                "nl", new LanguagePatterns
                {
                    SuffixPattern = @"\b(NV|BV|Vof)\b",
                    ConjunctionPattern = @"\b(en|van|in|voor|of)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Polish
            {
                "pl", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Sp\.z\.o\.o\.|SA|s\.c\.)\b",
                    ConjunctionPattern = @"\b(i|w|z|lub)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Swedish
            {
                "sv", new LanguagePatterns
                {
                    SuffixPattern = @"\b(AB)\b",
                    ConjunctionPattern = @"\b(och|i|för|eller)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Danish
            {
                "da", new LanguagePatterns
                {
                    SuffixPattern = @"\b(A\/S|ApS)\b",
                    ConjunctionPattern = @"\b(og|i|for|eller)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Finnish
            {
                "fi", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Oy|OYJ)\b",
                    ConjunctionPattern = @"\b(ja|in|tai)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Czech
            {
                "cs", new LanguagePatterns
                {
                    SuffixPattern = @"\b(s\.r\.o\.|a\.s\.)\b",
                    ConjunctionPattern = @"\b(a|v|z|nebo)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Slovak
            {
                "sk", new LanguagePatterns
                {
                    SuffixPattern = @"\b(s\.r\.o\.)\b",
                    ConjunctionPattern = @"\b(a|v|z|alebo)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Greek
            {
                "el", new LanguagePatterns
                {
                    SuffixPattern = @"\b(ΑΕ|ΕΠΕ)\b",
                    ConjunctionPattern = @"\b(και|σε|για|ή)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Bulgarian
            {
                "bg", new LanguagePatterns
                {
                    SuffixPattern = @"\b(ЕООД|АД)\b",
                    ConjunctionPattern = @"\b(и|в|за|или)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Hungarian
            {
                "hu", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Kft|Zrt|Nyrt)\b",
                    ConjunctionPattern = @"\b(és|ban|ben|val|vel|vagy)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Croatian
            {
                "hr", new LanguagePatterns
                {
                    SuffixPattern = @"\b(d\.o\.o\.|d\.d\.)\b",
                    ConjunctionPattern = @"\b(i|u|za|ili)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Estonian
            {
                "et", new LanguagePatterns
                {
                    SuffixPattern = @"\b(OÜ|AS)\b",
                    ConjunctionPattern = @"\b(ja|või)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Latvian
            {
                "lv", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SIA|AS)\b",
                    ConjunctionPattern = @"\b(un|vai)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Lithuanian
            {
                "lt", new LanguagePatterns
                {
                    SuffixPattern = @"\b(UAB)\b",
                    ConjunctionPattern = @"\b(ir|ar)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Romanian
            {
                "ro", new LanguagePatterns
                {
                    SuffixPattern = @"\b(SRL|SA)\b",
                    ConjunctionPattern = @"\b(şi|în|pentru|sau)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Slovene
            {
                "sl", new LanguagePatterns
                {
                    SuffixPattern = @"\b(d\.o\.o\.|d\.d\.)\b",
                    ConjunctionPattern = @"\b(in|za|ali)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Maltese
            {
                "mt", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Ltd)\b",
                    ConjunctionPattern = @"\b(u|ta’|għal|jew)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Hindi
            {
                "hi", new LanguagePatterns
                {
                    SuffixPattern = @"\b(लिमिटेड|प्राइवेट|पब्लिक)\b",
                    ConjunctionPattern = @"\b(और|में|के|या)\b",
                    PossessivePattern = @"['’]s\b",
                }
            },
            // Malay
            {
                "ms", new LanguagePatterns
                {
                    SuffixPattern = @"\b(Sdn Bhd|Bhd)\b",
                    ConjunctionPattern = @"\b(dan|di|untuk|atau)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Mandarin
            {
                "zh", new LanguagePatterns
                {
                    SuffixPattern = @"\b(有限公司|股份公司)\b",
                    ConjunctionPattern = @"\b(和|在|为|或)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Tamil
            {
                "ta", new LanguagePatterns
                {
                    SuffixPattern = @"\b(லிமிடெட்|பிரைவேட்)\b",
                    ConjunctionPattern = @"\b(மற்றும்|இல்|அல்லது)\b",
                    PossessivePattern = @"['’]s\b"
                }
            },
            // Japanese
            {
                "ja", new LanguagePatterns
                {
                    // Match 株式会社 or 合同会社 at the start, or surrounded by spaces (handles both cases)
                    SuffixPattern = @"^(株式会社|合同会社)|\s(株式会社|合同会社)\s?",
                    ConjunctionPattern = @"(?:^|\s)(と|で|に|や|または)(?:\s|$)",
                    PossessivePattern = @"['’]s\b",
                }
            },

            // Add more languages as needed (e.g., Māori, Irish, Luxembourgish, etc.)
        };
            return wordSets;
        }

        // Initialize job title specific language-specific word sets
        private static Dictionary<string, JobTitleLanguagePatterns> InitializeJobTitleLanguageWordSets()
        {
            var jobTitleWordSets = new Dictionary<string, JobTitleLanguagePatterns>
            {
                // English
                {
                    "en", new JobTitleLanguagePatterns
                    {
                        ShiftPattern = @",\s*(Evening Shift|Day Shift|Night Shift|PT Day Shift|Variable Part Time Day Shift|Per Diem Night Shift|Per Diem Nights|Rotating Shifts)\b",
                        DepartmentPattern = @",\s*(Security Services|Heart and Vascular Unit|Float Pool|Med Surg General Surgery|Med Surg|Quality & Patient Safety|Behavioral Health \(Inpatient\)|Behavioral Health|Labor & Delivery|Critical Care Float|Cardiac Specialty Unit|Rehabilitation|Pharmacy|Transport Team)\b",
                        EmploymentTypePattern = @",\s*(Variable Part Time|PT|\d+\.\d+\s+FTE)\b",
                        LocationCodePattern = @"\s*-\s*\([A-Z]{2,5}(-[A-Z]{2,5})*\)",
                        IdentifierPattern = @"-?\d{4,5}\b|\b\d{4}\s+\d+\.\d+\s+FTE\b|\(\d{4,5}\)",
                        LocationDetailPattern = @"\s*-\s*[^,]+\s+(Shopping Center|Training Center|Brokerage)|,\s*[^,]+\s+(Shopping Center|Training Center|Brokerage)"
                    }
                },
                // Add more languages as needed
            };
            return jobTitleWordSets;
        }

        /// <summary>
        /// Optimized language detection with caching and static initialization
        /// </summary>
        /// <param name="text">Text to detect language for</param>
        /// <returns>Detected language code or "en" as fallback</returns>
        private static string DetectLanguageOptimized(string text)
        {
            if (string.IsNullOrEmpty(text))
                return "en";

            // Check cache first
            if (_languageCache.TryGetValue(text, out string cachedLanguage))
                return cachedLanguage;

            // Detect language using the static detector
            string detectedLanguage;
            try
            {
                detectedLanguage = _languageDetector.Value.Detect(text) ?? "en";
            }
            catch
            {
                // Fallback to English if detection fails
                detectedLanguage = "en";
            }

            // Cache the result if cache isn't too large
            if (_languageCache.Count < MaxCacheSize)
            {
                _languageCache.TryAdd(text, detectedLanguage);
            }

            return detectedLanguage;
        }

        public static string GetDeepCleanCompanyName(string companyName)
        {
            if (string.IsNullOrEmpty(companyName)) return string.Empty;

            // Use optimized language detection with caching
            string detectedLanguage = DetectLanguageOptimized(companyName);

            var cleanCompanyName = companyName;

            // Step 1: Remove parentheses content and special characters (language-independent)
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"\(.*?\)", "");
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"[""']", "");

            // Step 2: Handle websites (language-independent)
            var matchWebSite = Regex.Match(cleanCompanyName, @"\p{L}+\.(com|co|org|net|edu|gov|mil|biz|info|eu|de|fr|uk|es|it|jp|cn|in|sg|au|nz)", RegexOptions.IgnoreCase);
            if (matchWebSite.Success)
                return matchWebSite.Value;

            // Step 3: Apply language-specific patterns
            if (LanguageWordSets.TryGetValue(detectedLanguage, out var langPatterns))
            {
                // Remove corporate suffixes for detected language first
                cleanCompanyName = Regex.Replace(cleanCompanyName, langPatterns.SuffixPattern, "", RegexOptions.IgnoreCase);

                // Remove conjunctions and possessives for detected language
                var combinedPattern = $@"({langPatterns.ConjunctionPattern}|{langPatterns.PossessivePattern})";
                cleanCompanyName = Regex.Replace(cleanCompanyName, combinedPattern, " ", RegexOptions.IgnoreCase);
            }

            // Step 4: Apply common English patterns as fallback
            if (detectedLanguage != "en" && LanguageWordSets.TryGetValue("en", out var enPatterns))
            {
                cleanCompanyName = Regex.Replace(cleanCompanyName, enPatterns.SuffixPattern, "", RegexOptions.IgnoreCase);
            }

            // Step 5: Remove leftover symbols like . , ; : at the end or between words (language-independent)
            cleanCompanyName = Regex.Replace(cleanCompanyName, "[.,;:]+", "");

            // Step 6: Handle common abbreviations (language-independent)
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"\b(Grp|Gp)\b", "Group", RegexOptions.IgnoreCase);
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"®|™|©", "", RegexOptions.IgnoreCase);

            // Step 7: Clean up whitespace and special characters
            cleanCompanyName = Regex.Replace(cleanCompanyName, @"\s+", " ").Trim();

            // Step 8: Tokenize and singularize (with blacklist)
            cleanCompanyName = ApplyTokenizeAndSingularize(cleanCompanyName, detectedLanguage);

            // Final trim to remove any leading/trailing spaces before returning
            cleanCompanyName = cleanCompanyName.Trim();

            return string.IsNullOrWhiteSpace(cleanCompanyName) ? companyName : cleanCompanyName;
        }

        /// <summary>
        /// Applies tokenization and singularization to the input string based on the detected language.
        /// </summary>
        /// <param name="input"></param>
        /// <param name="detectedLanguage"></param>
        /// <returns></returns>
        private static string ApplyTokenizeAndSingularize(string input, string detectedLanguage)
        {
            // Blacklist: languages for which this step should NOT run
            var blacklist = new HashSet<string> { "hi" }; // Add more language codes as needed
            if (blacklist.Contains(detectedLanguage))
                return input;

            // Tokenize using Unicode letters
            var words = Regex.Matches(input, @"\p{L}+").OfType<Match>().Select(m => m.Value).ToArray();
            // Singularize: if more than 1 word and word length > 6, trim trailing 's'
            var result = string.Join(" ", words.Select(a => words.Length > 1 && a.Length > 6 ? a.TrimEnd('s') : a));
            return result;
        }

        /// <summary>
        /// Cleans job posting titles by removing noise, standardizing formatting, and normalizing job titles
        /// </summary>
        /// <param name="jobTitle">The job title to clean</param>
        /// <returns>Cleaned job title</returns>
        public static string GetCleanJobPostingTitle(string jobTitle)
        {
            if (string.IsNullOrEmpty(jobTitle)) return string.Empty;

            // Use optimized language detection with caching
            string detectedLanguage = DetectLanguageOptimized(jobTitle);

            var cleanJobTitle = jobTitle;

            // Step 1: Remove location codes in parentheses (e.g., "(LAC)", "(PVSP)")
            if (JobTitleLanguageWordSets.TryGetValue(detectedLanguage, out var jobLangPatterns))
            {
                cleanJobTitle = Regex.Replace(cleanJobTitle, jobLangPatterns.LocationCodePattern, "", RegexOptions.IgnoreCase);
            }

            // Step 2: Remove shift information
            if (jobLangPatterns != null)
            {
                cleanJobTitle = Regex.Replace(cleanJobTitle, jobLangPatterns.ShiftPattern, "", RegexOptions.IgnoreCase);
            }

            // Step 3: Remove department/unit information
            if (jobLangPatterns != null)
            {
                cleanJobTitle = Regex.Replace(cleanJobTitle, jobLangPatterns.DepartmentPattern, "", RegexOptions.IgnoreCase);
            }

            // Step 4: Remove employment type indicators
            if (jobLangPatterns != null)
            {
                cleanJobTitle = Regex.Replace(cleanJobTitle, jobLangPatterns.EmploymentTypePattern, "", RegexOptions.IgnoreCase);
            }

            // Step 5: Remove specific identifiers and numbers
            if (jobLangPatterns != null)
            {
                cleanJobTitle = Regex.Replace(cleanJobTitle, jobLangPatterns.IdentifierPattern, "", RegexOptions.IgnoreCase);
            }

            // Step 6: Remove location details
            if (jobLangPatterns != null)
            {
                cleanJobTitle = Regex.Replace(cleanJobTitle, jobLangPatterns.LocationDetailPattern, "", RegexOptions.IgnoreCase);
            }

            // Step 7: Handle specific patterns that need special treatment
            cleanJobTitle = HandleSpecialJobTitlePatterns(cleanJobTitle);

            // Step 8: Apply job title standardizations
            cleanJobTitle = ApplyJobTitleStandardizations(cleanJobTitle);

            // Step 9: Handle parentheses - preserve certifications but remove other content
            cleanJobTitle = CleanJobTitleParentheses(cleanJobTitle);

            // Step 10: Clean up punctuation and whitespace
            cleanJobTitle = Regex.Replace(cleanJobTitle, @"[,;:]+", ",");
            cleanJobTitle = Regex.Replace(cleanJobTitle, @"\s*-\s*", " ");
            cleanJobTitle = Regex.Replace(cleanJobTitle, @"\s+", " ").Trim();
            cleanJobTitle = Regex.Replace(cleanJobTitle, @"^,\s*|,\s*$", "").Trim(); // Remove leading/trailing commas

            // Final cleanup
            cleanJobTitle = cleanJobTitle.Trim();

            return string.IsNullOrWhiteSpace(cleanJobTitle) ? jobTitle : cleanJobTitle;
        }

        /// <summary>
        /// Applies job title specific standardizations
        /// </summary>
        /// <param name="jobTitle">The job title to standardize</param>
        /// <returns>Standardized job title</returns>
        private static string ApplyJobTitleStandardizations(string jobTitle)
        {
            // VP standardization
            jobTitle = Regex.Replace(jobTitle, @"\bVP\b", "Vice President", RegexOptions.IgnoreCase);

            // Specific business rule transformations
            jobTitle = Regex.Replace(jobTitle, @"\bFloors\b", "Flooring Specialist", RegexOptions.IgnoreCase);

            // Handle specific patterns that need special treatment
            jobTitle = Regex.Replace(jobTitle, @"^Catering\s*-\s*", "", RegexOptions.IgnoreCase);

            // Only remove prefix before dash for specific cases, not all cases
            if (jobTitle.Contains("Intensive Case Manager"))
            {
                jobTitle = Regex.Replace(jobTitle, @"^[^-]+-\s*", "", RegexOptions.IgnoreCase);
            }

            return jobTitle;
        }

        /// <summary>
        /// Cleans parentheses in job titles, preserving certifications but removing other content
        /// </summary>
        /// <param name="jobTitle">The job title to clean</param>
        /// <returns>Job title with cleaned parentheses</returns>
        private static string CleanJobTitleParentheses(string jobTitle)
        {
            // Preserve important certifications and abbreviations
            var preservePattern = @"\b(RN|CNA|LCSW|LAC|OR)\b";

            // Find all parentheses content
            var matches = Regex.Matches(jobTitle, @"\(([^)]+)\)");

            foreach (Match match in matches)
            {
                var content = match.Groups[1].Value;

                // If the content contains important certifications, keep it
                if (!Regex.IsMatch(content, preservePattern, RegexOptions.IgnoreCase))
                {
                    // Remove this parentheses
                    jobTitle = jobTitle.Replace(match.Value, "");
                }
            }

            return jobTitle;
        }

        /// <summary>
        /// Handles special job title patterns that need custom processing
        /// </summary>
        /// <param name="jobTitle">The job title to process</param>
        /// <returns>Processed job title</returns>
        private static string HandleSpecialJobTitlePatterns(string jobTitle)
        {
            // Handle "Operating Room" preservation
            if (jobTitle.Contains("Operating Room"))
            {
                jobTitle = Regex.Replace(jobTitle, @".*Operating Room.*", match =>
                {
                    var result = Regex.Replace(match.Value, @"^[^-]+-\s*", "");
                    return result.Replace("Operating Room", ", Operating Room");
                });
            }

            // Handle "Critical Care Float" preservation
            if (jobTitle.Contains("Critical Care Float"))
            {
                jobTitle = Regex.Replace(jobTitle, @".*Critical Care Float.*", match =>
                {
                    var result = Regex.Replace(match.Value, @"^[^-]+-\s*", "");
                    return result.Replace("Critical Care Float", ", Critical Care Float");
                });
            }

            // Handle "Cardiac Specialty Unit" preservation
            if (jobTitle.Contains("Cardiac Specialty Unit"))
            {
                jobTitle = Regex.Replace(jobTitle, @".*Cardiac Specialty Unit.*", match =>
                {
                    var result = Regex.Replace(match.Value, @"^[^-]+-\s*", "");
                    return result.Replace("Cardiac Specialty Unit", ", Cardiac Specialty Unit");
                });
            }

            return jobTitle;
        }

    }
}